#!/bin/bash
# Dummy script to simulate setting local parameters
# This would normally modify configuration files

param_name=$1
param_value=$2

echo "Setting $param_name to $param_value"

# In a real implementation, this would modify actual configuration files
# For now, just echo the parameters
case $param_name in
    "nlocMMFF")
        echo "nlocMMFF set to $param_value"
        ;;
    "nlocmove")
        echo "nlocmove set to $param_value"
        ;;
    "nlocNBFF")
        echo "nlocNBFF set to $param_value"
        ;;
    "nlocSurf")
        echo "nlocSurf set to $param_value"
        ;;
    "nlocGridFF")
        echo "nlocGridFF set to $param_value"
        ;;
    "nlocGridFFbSpline")
        echo "nlocGridFFbSpline set to $param_value"
        ;;
    *)
        echo "Unknown parameter: $param_name"
        ;;
esac
