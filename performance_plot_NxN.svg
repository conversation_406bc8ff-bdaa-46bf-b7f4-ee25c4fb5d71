<svg class="main-svg" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="700" height="700" style="" viewBox="0 0 700 700"><rect x="0" y="0" width="700" height="700" style="fill: rgb(255, 255, 255); fill-opacity: 1;"/><defs id="defs-c02169"><g class="clips"><clipPath id="clipc02169xyplot" class="plotclip"><rect width="540" height="500"/></clipPath><clipPath class="axesclip" id="clipc02169x"><rect x="80" y="0" width="540" height="700"/></clipPath><clipPath class="axesclip" id="clipc02169y"><rect x="0" y="100" width="700" height="500"/></clipPath><clipPath class="axesclip" id="clipc02169xy"><rect x="80" y="100" width="540" height="500"/></clipPath></g><g class="gradients"/><g class="patterns"/></defs><g class="bglayer"><rect class="bg" x="80" y="100" width="540" height="500" style="fill: rgb(240, 248, 255); fill-opacity: 0.95; stroke-width: 0;"/></g><g class="layer-below"><g class="imagelayer"/><g class="shapelayer"/></g><g class="cartesianlayer"><g class="subplot xy"><g class="layer-subplot"><g class="shapelayer"/><g class="imagelayer"/></g><g class="minor-gridlayer"><g class="x"/><g class="y"/></g><g class="gridlayer"><g class="x"><path class="xgrid crisp" transform="translate(114.5,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(145.9,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(177.3,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(208.7,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(240.1,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(271.5,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(302.9,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(334.3,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(365.7,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(397.1,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(428.5,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(459.9,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(491.3,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(522.7,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(554.1,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(585.5,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g><g class="y"><path class="ygrid crisp" transform="translate(0,507)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,446.25)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,385.5)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,324.75)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,263.99)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,203.24)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,142.49)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g></g><g class="zerolinelayer"><path class="yzl zl crisp" transform="translate(0,567.75)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;"/></g><g class="layer-between"><g class="shapelayer"/><g class="imagelayer"/></g><path class="xlines-below"/><path class="ylines-below"/><g class="overlines-below"/><g class="xaxislayer-below"/><g class="yaxislayer-below"/><g class="overaxes-below"/><g class="overplot"><g class="xy" transform="translate(80,100)" clip-path="url(#clipc02169xyplot)"><g class="scatterlayer mlayer"><g class="trace scatter traceecf9d1" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,219.1L65.9,369.76L97.3,434.88L128.7,455.75L160.1,459.81L191.5,463.99L222.9,465.42L317.1,467.05L348.5,467.15L505.5,467.51" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,219.1)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(65.9,369.76)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(97.3,434.88)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(128.7,455.75)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(160.1,459.81)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(191.5,463.99)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(222.9,465.42)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(254.3,466.05)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(285.7,466.8)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(317.1,467.05)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(348.5,467.15)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(379.9,467.15)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(411.3,467.39)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(442.7,467.43)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(474.1,467.46)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(505.5,467.51)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter tracede99fa" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,252.39L65.9,329.97L97.3,432.75L128.7,453.38L160.1,459.98L191.5,463.64L222.9,465.62L317.1,466.94L348.5,466.92L474.1,467.49L505.5,467.52" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,252.39)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(65.9,329.97)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(97.3,432.75)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(128.7,453.38)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(160.1,459.98)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(191.5,463.64)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(222.9,465.62)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(254.3,466.2)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(285.7,466.21)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(317.1,466.94)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(348.5,466.92)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(379.9,467.22)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(411.3,467.24)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(442.7,467.41)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(474.1,467.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(505.5,467.52)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter traceb3e187" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,248.62L65.9,370.68L97.3,442.55L128.7,458.55L160.1,463.19L191.5,465.18L222.9,466.1L285.7,466.78L317.1,466.98L442.7,467.57L474.1,467.54L505.5,467.57" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,248.62)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(65.9,370.68)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(97.3,442.55)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(128.7,458.55)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(160.1,463.19)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(191.5,465.18)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(222.9,466.1)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(254.3,466.09)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(285.7,466.78)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(317.1,466.98)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(348.5,467.3)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(379.9,467.36)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(411.3,467.34)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(442.7,467.57)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(474.1,467.54)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(505.5,467.57)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace6bf9b9" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,209.86L65.9,371.68L97.3,438.44L128.7,456.13L160.1,462.58L222.9,466.02L254.3,466.36L317.1,466.98L348.5,467.23L505.5,467.57" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,209.86)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(65.9,371.68)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(97.3,438.44)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(128.7,456.13)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(160.1,462.58)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(191.5,464.63)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(222.9,466.02)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(254.3,466.36)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(285.7,466.92)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(317.1,466.98)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(348.5,467.23)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(379.9,467.33)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(411.3,467.36)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(442.7,467.48)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(474.1,467.59)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(505.5,467.57)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace9cc139" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,132.4L65.9,340.45L97.3,431.83L160.1,459.75L191.5,461.88L222.9,464.21L254.3,465.68L411.3,467.22L442.7,467.35L505.5,467.53" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,132.4)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(65.9,340.45)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(97.3,431.83)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(128.7,445.89)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(160.1,459.75)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(191.5,461.88)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(222.9,464.21)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(254.3,465.68)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(285.7,466.1)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(317.1,466.66)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(348.5,466.83)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(379.9,467.06)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(411.3,467.22)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(442.7,467.35)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(474.1,467.44)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(505.5,467.53)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter traceea5a98" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,149.23L65.9,340.7L97.3,419.1L128.7,448.08L160.1,460.57L222.9,464.73L254.3,465.28L317.1,466.73L348.5,466.83L505.5,467.5" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,149.23)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(65.9,340.7)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(97.3,419.1)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(128.7,448.08)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(160.1,460.57)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(191.5,462.69)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(222.9,464.73)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(254.3,465.28)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(285.7,466.18)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(317.1,466.73)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(348.5,466.83)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(379.9,467.1)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(411.3,467.18)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(442.7,467.29)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(474.1,467.37)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(505.5,467.5)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter traced9d6c8" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,31.25L65.9,356.09L97.3,423.05L128.7,451.32L160.1,459.59L191.5,462.85L222.9,464L285.7,466.71L317.1,466.49L348.5,467.19L379.9,467.08L474.1,467.51L505.5,467.56" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,31.25)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(65.9,356.09)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(97.3,423.05)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(128.7,451.32)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(160.1,459.59)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(191.5,462.85)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(222.9,464)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(254.3,465.69)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(285.7,466.71)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(317.1,466.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(348.5,467.19)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(379.9,467.08)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(411.3,467.42)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(442.7,467.31)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(474.1,467.51)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(505.5,467.56)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter tracec94e6e" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,130.7L65.9,352.7L97.3,438.77L128.7,452.9L160.1,461.46L191.5,462.36L222.9,464.46L285.7,466.83L317.1,466.85L505.5,467.56" style="vector-effect: none; fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,130.7)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(65.9,352.7)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(97.3,438.77)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(128.7,452.9)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(160.1,461.46)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(191.5,462.36)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(222.9,464.46)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(254.3,465.79)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(285.7,466.83)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(317.1,466.85)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(348.5,466.87)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(379.9,467.34)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(411.3,467.33)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(442.7,467.44)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(474.1,467.48)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/><path class="point" transform="translate(505.5,467.56)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g><g class="text"/></g></g></g></g><path class="xlines-above crisp" d="M0,0" style="fill: none;"/><path class="ylines-above crisp" d="M0,0" style="fill: none;"/><g class="overlines-above"/><g class="xaxislayer-above"><g class="xtick"><text text-anchor="start" x="0" y="615" transform="translate(114.5,0) rotate(29.999999999999996,0,608)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">1x1</text></g><g class="xtick"><text text-anchor="start" x="0" y="615" transform="translate(145.9,0) rotate(29.999999999999996,0,608)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">2x2</text></g><g class="xtick"><text text-anchor="start" x="0" y="615" transform="translate(177.3,0) rotate(29.999999999999996,0,608)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">3x3</text></g><g class="xtick"><text text-anchor="start" x="0" y="615" transform="translate(208.7,0) rotate(29.999999999999996,0,608)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">4x4</text></g><g class="xtick"><text text-anchor="start" x="0" y="615" transform="translate(240.1,0) rotate(29.999999999999996,0,608)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">5x5</text></g><g class="xtick"><text text-anchor="start" x="0" y="615" transform="translate(271.5,0) rotate(29.999999999999996,0,608)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">6x6</text></g><g class="xtick"><text text-anchor="start" x="0" y="615" transform="translate(302.9,0) rotate(29.999999999999996,0,608)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">7x7</text></g><g class="xtick"><text text-anchor="start" x="0" y="615" transform="translate(334.3,0) rotate(29.999999999999996,0,608)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">8x8</text></g><g class="xtick"><text text-anchor="start" x="0" y="615" transform="translate(365.7,0) rotate(29.999999999999996,0,608)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">9x9</text></g><g class="xtick"><text text-anchor="start" x="0" y="615" transform="translate(397.1,0) rotate(29.999999999999996,0,608)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">10x10</text></g><g class="xtick"><text text-anchor="start" x="0" y="615" transform="translate(428.5,0) rotate(29.999999999999996,0,608)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">11x11</text></g><g class="xtick"><text text-anchor="start" x="0" y="615" transform="translate(459.9,0) rotate(29.999999999999996,0,608)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">12x12</text></g><g class="xtick"><text text-anchor="start" x="0" y="615" transform="translate(491.3,0) rotate(29.999999999999996,0,608)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">13x13</text></g><g class="xtick"><text text-anchor="start" x="0" y="615" transform="translate(522.7,0) rotate(29.999999999999996,0,608)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">14x14</text></g><g class="xtick"><text text-anchor="start" x="0" y="615" transform="translate(554.1,0) rotate(29.999999999999996,0,608)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">15x15</text></g><g class="xtick"><text text-anchor="start" x="0" y="615" transform="translate(585.5,0) rotate(29.999999999999996,0,608)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">16x16</text></g></g><g class="yaxislayer-above"><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" transform="translate(0,567.75)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">0</text></g><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,507)">100</text></g><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,446.25)">200</text></g><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,385.5)">300</text></g><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,324.75)">400</text></g><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,263.99)">500</text></g><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,203.24)">600</text></g><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,142.49)">700</text></g></g><g class="overaxes-above"/></g></g><g class="polarlayer"/><g class="smithlayer"/><g class="ternarylayer"/><g class="geolayer"/><g class="funnelarealayer"/><g class="pielayer"/><g class="iciclelayer"/><g class="treemaplayer"/><g class="sunburstlayer"/><g class="glimages"/><defs id="topdefs-c02169"><g class="clips"/><clipPath id="legendc02169"><rect width="172" height="32" x="0" y="0"/></clipPath></defs><g class="layer-above"><g class="imagelayer"/><g class="shapelayer"/></g><g class="infolayer"><g class="legend" pointer-events="all" transform="translate(448,57.999999999999986)"><rect class="bg" shape-rendering="crispEdges" width="172" height="32" x="0" y="0" style="stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;"/><g class="scrollbox" transform="" clip-path="url(#legendc02169)"><g class="groups"><g class="traces" transform="translate(0,15.6)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="5.46" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">GridFF with BSpline</text><g class="layers" style="opacity: 0.3;"><g class="legendfill"/><g class="legendlines"><path class="js-line" d="M5,0h30" style="fill: none; stroke: rgb(0, 0, 255); stroke-opacity: 1; stroke-width: 2px;"/></g><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 0, 255); fill-opacity: 1; stroke: rgb(0, 0, 255); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-10.6" width="169.234375" height="21.2" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g></g></g><rect class="scrollbar" rx="20" ry="3" width="0" height="0" x="0" y="0" style="fill: rgb(128, 139, 164); fill-opacity: 1;"/></g><g class="g-gtitle"><text class="gtitle" x="35" y="50" text-anchor="start" dy="0em" style="opacity: 1; font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 24px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Evaluations per second vs. Surface Size</text></g><g class="g-xtitle"><text class="xtitle" x="350" y="664.3060546875" text-anchor="middle" style="opacity: 1; font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 18px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Surface Size</text></g><g class="g-ytitle"><text class="ytitle" transform="rotate(-90,34.184375,350)" x="34.184375" y="350" text-anchor="middle" style="opacity: 1; font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 18px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Evaluations per second</text></g></g><g class="menulayer"><g class="updatemenu-container" style="cursor: pointer;"><g class="updatemenu-header-group" pointer-events="all"/><g class="updatemenu-dropdown-button-group" updatemenu-active-index="-1" style="pointer-events: all;"><rect class="scrollbox-bg" opacity="0" x="0" y="0" width="0" height="0" style="pointer-events: all;"/></g></g></g></svg>