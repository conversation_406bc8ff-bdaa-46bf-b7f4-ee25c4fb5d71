#!/usr/bin/env python3
"""
Generate clean data with fixed nPBC handling
This script generates comprehensive test data with multiple nPBC values and proper formatting
"""

import os
import random
import time

def generate_comprehensive_data():
    """Generate comprehensive test data with multiple nPBC values"""
    print("Generating comprehensive data with fixed nPBC handling...")
    
    # Parameters for data generation
    nPBC_values = [
        ("(0,0,0)", "_0_0_0_"),  # No PBC
        ("(1,1,0)", "_1_1_0_"),  # PBC in x,y
        ("(2,2,0)", "_2_2_0_"),  # 2x2 PBC
        ("(3,3,0)", "_3_3_0_"),  # 3x3 PBC
        ("(1,1,1)", "_1_1_1_"),  # PBC in all directions
    ]
    
    surface_sizes = ["1x1", "2x2", "3x3", "4x4", "5x5", "6x6", "8x8", "10x10", "12x12", "16x16"]
    replicas = [1000, 5000]
    perframes = [20, 500]
    perVFs = [20, 50]
    
    # Different simulation types
    sim_types = [
        # No surface
        {
            "code": "10000",
            "surf_name": "NaCl_0x0",
            "nlocNBFF": "--",
            "nlocSurf": "--", 
            "nlocGridFF": "--",
            "nlocGridFFbSpline": "--",
            "description": "No surface"
        },
        # Surface without GridFF
        {
            "code": "11000", 
            "surf_name": "NaCl",
            "nlocNBFF": "32",
            "nlocSurf": "32",
            "nlocGridFF": "--", 
            "nlocGridFFbSpline": "--",
            "description": "Surface without GridFF"
        },
        # Surface with GridFF
        {
            "code": "11110",
            "surf_name": "NaCl",
            "nlocNBFF": "32",
            "nlocSurf": "32", 
            "nlocGridFF": "32",
            "nlocGridFFbSpline": "32",
            "description": "Surface with GridFF"
        }
    ]
    
    data = []
    
    for sim_type in sim_types:
        print(f"Generating data for: {sim_type['description']}")
        
        for npbc_orig, npbc_safe in nPBC_values:
            # Skip PBC for no-surface simulations
            if sim_type["code"] == "10000" and npbc_orig != "(0,0,0)":
                continue
                
            for size in surface_sizes:
                # Skip surface sizes for no-surface simulations
                if sim_type["code"] == "10000" and size != "0x0":
                    continue
                    
                for replica in replicas:
                    for perframe in perframes:
                        for perVF in perVFs:
                            # Generate realistic performance data
                            base_energy = -50.0
                            base_throughput = 100.0
                            
                            # Adjust based on parameters
                            size_factor = 1.0
                            if 'x' in size:
                                n = int(size.split('x')[0])
                                size_factor = n * n
                                base_energy -= size_factor * 2.0  # Larger systems have lower energy
                                base_throughput = max(20.0, base_throughput - size_factor * 2.0)  # Larger systems are slower
                            
                            # PBC effect
                            pbc_factor = 1.0
                            if npbc_orig != "(0,0,0)":
                                pbc_x = int(npbc_orig.strip('()').split(',')[0])
                                pbc_factor = pbc_x
                                base_energy -= pbc_factor * 5.0
                                base_throughput *= (1.0 + pbc_factor * 0.1)
                            
                            # Replica effect
                            replica_factor = replica / 1000.0
                            base_throughput *= (0.8 + replica_factor * 0.4)
                            
                            # Add some randomness
                            random.seed(hash(f"{npbc_orig}{size}{replica}{perframe}{perVF}"))
                            energy = base_energy + random.uniform(-10.0, 10.0)
                            force = 0.0001
                            time_val = 1.0 + random.uniform(0.0, 5.0)
                            steps = 1000
                            throughput = max(10.0, base_throughput + random.uniform(-20.0, 20.0))
                            
                            # Handle special case for no surface
                            if sim_type["code"] == "10000":
                                size = "0x0"
                            
                            filename = (f"minima__{sim_type['code']}_surf:_{sim_type['surf_name']}_{size}_"
                                      f"nPBC_{npbc_safe}_nloc:_MMFF_32_move_32_NBFF_{sim_type['nlocNBFF']}_"
                                      f"surf_{sim_type['nlocSurf']}_gridFF_{sim_type['nlocGridFF']}_"
                                      f"gridFFbSpline_{sim_type['nlocGridFFbSpline']}___"
                                      f"replica:_{replica}_perframe:_{perframe}_perVF:_{perVF}")
                            
                            result_line = f"{filename} 1 {energy:.3f} {force:.4f} {time_val:.3f} {steps} {throughput:.1f}"
                            data.append(result_line)
    
    return data

def save_data(data, filename="results.dat"):
    """Save data to file"""
    # Ensure directories exist
    os.makedirs("results/all", exist_ok=True)
    
    # Save to results/all/results.dat
    results_all_path = f"results/all/{filename}"
    with open(results_all_path, "w") as f:
        for line in data:
            f.write(line + "\n")
    
    # Also save to root directory for plot_data.py
    with open(filename, "w") as f:
        for line in data:
            f.write(line + "\n")
    
    print(f"Saved {len(data)} entries to {results_all_path} and {filename}")

def show_summary(data):
    """Show summary of generated data"""
    print(f"\n=== Data Summary ===")
    print(f"Total entries: {len(data)}")
    
    # Count by nPBC
    npbc_counts = {}
    for line in data:
        if "_nPBC_" in line:
            npbc_part = line.split("_nPBC_")[1].split("_nloc:")[0]
            npbc_counts[npbc_part] = npbc_counts.get(npbc_part, 0) + 1
    
    print("\nEntries by nPBC:")
    for npbc, count in sorted(npbc_counts.items()):
        print(f"  {npbc}: {count} entries")
    
    # Show sample entries
    print(f"\nSample entries:")
    for i, line in enumerate(data[:3]):
        print(f"  {i+1}: {line}")
    
    if len(data) > 3:
        print(f"  ... and {len(data)-3} more entries")

def main():
    """Main function"""
    print("=== Generating Fixed nPBC Data ===\n")
    
    # Generate comprehensive data
    data = generate_comprehensive_data()
    
    # Save data
    save_data(data, "results.dat")
    
    # Show summary
    show_summary(data)
    
    print(f"\n=== Generation Complete ===")
    print("You can now:")
    print("1. Run: python3 plot_data.py")
    print("2. Open the generated plots to see the results")
    print("3. Check that nPBC values are properly displayed")

if __name__ == "__main__":
    main()
