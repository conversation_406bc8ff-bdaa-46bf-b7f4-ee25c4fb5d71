#!/usr/bin/env python3
"""
Test script to verify nPBC fixes
This script tests the new nPBC handling and generates sample data
"""

import os
import subprocess
import time

def test_npbc_formatting():
    """Test nPBC formatting functions"""
    print("Testing nPBC formatting...")
    
    # Test cases
    test_cases = [
        "(1,1,0)",
        "(2,2,0)", 
        "(3,3,0)",
        "(0,0,0)"
    ]
    
    for npbc in test_cases:
        # Test bash-style escaping
        npbc_safe = npbc.replace('(', '_').replace(')', '_').replace(',', '_')
        print(f"Original: {npbc} -> Safe: {npbc_safe}")
        
        # Test filename generation
        filename = f"minima__1110_surf:_NaCl_4x4_nPBC_{npbc_safe}_nloc:_MMFF_32_move_32_NBFF_--_surf_--_gridFF_--_gridFFbSpline_32___replica:_1000_perframe:_20_perVF:_20"
        print(f"  Filename: {filename}")
        print()

def generate_test_data():
    """Generate test data with multiple nPBC values"""
    print("Generating test data with multiple nPBC values...")
    
    # Create test data
    test_data = []
    
    npbc_values = ["_1_1_0_", "_2_2_0_", "_3_3_0_", "_0_0_0_"]
    surface_sizes = ["1x1", "2x2", "4x4", "8x8"]
    replicas = [1000, 5000]
    perframes = [20, 500]
    perVFs = [20, 50]
    
    for npbc in npbc_values:
        for size in surface_sizes:
            for replica in replicas:
                for perframe in perframes:
                    for perVF in perVFs:
                        # Generate realistic test values
                        energy = -50.0 + (hash(f"{npbc}{size}{replica}") % 100) / 10.0
                        force = 0.0001
                        time_val = 1.0 + (hash(f"{size}{replica}") % 50) / 10.0
                        steps = 1000
                        throughput = 100.0 + (hash(f"{replica}{perframe}") % 500) / 10.0
                        
                        filename = f"minima__11110_surf:_NaCl_{size}_nPBC_{npbc}_nloc:_MMFF_32_move_32_NBFF_32_surf_32_gridFF_32_gridFFbSpline_32___replica:_{replica}_perframe:_{perframe}_perVF:_{perVF}"
                        
                        result_line = f"{filename} 1 {energy:.3f} {force:.4f} {time_val:.3f} {steps} {throughput:.1f}"
                        test_data.append(result_line)
    
    # Write test data
    os.makedirs("results/all", exist_ok=True)
    
    with open("results/all/results_test.dat", "w") as f:
        for line in test_data:
            f.write(line + "\n")
    
    print(f"Generated {len(test_data)} test entries in results/all/results_test.dat")
    
    # Show sample entries
    print("\nSample entries:")
    for i, line in enumerate(test_data[:5]):
        print(f"  {i+1}: {line}")
    
    return len(test_data)

def test_parsing():
    """Test parsing of the new format"""
    print("\nTesting parsing of new nPBC format...")
    
    # Test the parsing logic from plot_data.py
    test_values = ["_1_1_0_", "_2_2_0_", "_3_3_0_", "_0_0_0_", "(1,1,0)", "(2,2,0)"]
    
    for npbc_val in test_values:
        npbc_x = 0
        
        # Handle format like (1,1,0) or _1_1_0_ (escaped format)
        if '(' in npbc_val and ')' in npbc_val:
            # Extract values within parentheses and split by comma
            inner_values = npbc_val.strip('()').split(',')
            if len(inner_values) >= 1:
                npbc_x = int(inner_values[0])
        elif npbc_val.startswith('_') and npbc_val.endswith('_'):
            # Handle escaped format like _1_1_0_
            parts = npbc_val.strip('_').split('_')
            if len(parts) >= 1 and parts[0].isdigit():
                npbc_x = int(parts[0])
        
        print(f"  {npbc_val} -> npbc_x = {npbc_x}")

def main():
    """Main test function"""
    print("=== Testing nPBC Fixes ===\n")
    
    # Test formatting
    test_npbc_formatting()
    
    # Generate test data
    num_entries = generate_test_data()
    
    # Test parsing
    test_parsing()
    
    print(f"\n=== Test Complete ===")
    print(f"Generated {num_entries} test entries")
    print("Check results/all/results_test.dat for the generated data")
    print("\nTo test with plot_data.py:")
    print("1. Copy results_test.dat to results.dat")
    print("2. Run: python3 plot_data.py")

if __name__ == "__main__":
    main()
