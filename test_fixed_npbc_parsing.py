#!/usr/bin/env python3
"""
Test the fixed nPBC parsing logic
"""

def parse_npbc_fixed(npbc_input):
    """Fixed nPBC parsing logic from GUI"""
    npbc_input = npbc_input.strip()
    nPBCs = []
    if npbc_input:
        # Split by "),(" to handle multiple nPBC values like "(1,1,0),(2,2,0)"
        if '),(' in npbc_input:
            parts = npbc_input.split('),(')
            for i, part in enumerate(parts):
                if i == 0:
                    part = part + ')'  # Add closing parenthesis to first part
                elif i == len(parts) - 1:
                    part = '(' + part  # Add opening parenthesis to last part
                else:
                    part = '(' + part + ')'  # Add both parentheses to middle parts
                nPBCs.append(part.strip())
        else:
            # Single nPBC value
            nPBCs.append(npbc_input)
    return nPBCs

def test_fixed_parsing():
    """Test the fixed parsing logic"""
    print("Testing FIXED nPBC parsing...")
    
    test_cases = [
        "(0,0,0)",
        "(1,1,0)",
        "(0,0,0),(1,1,0)",
        "(1,1,0),(2,2,0),(3,3,0)",
        "(1,1,0),(2,2,0),(3,3,0),(0,0,0)"
    ]
    
    for test_input in test_cases:
        print(f"\nInput: {test_input}")
        
        # Test fixed parsing
        nPBCs = parse_npbc_fixed(test_input)
        print(f"Parsed nPBCs: {nPBCs}")
        
        # Test filename generation for each nPBC
        for nPBC in nPBCs:
            nPBC_safe = nPBC.replace('(', '_').replace(')', '_').replace(',', '_')
            print(f"  {nPBC} -> {nPBC_safe}")

def main():
    print("=== Testing Fixed nPBC Parsing ===")
    test_fixed_parsing()
    print("\n=== Test Complete ===")

if __name__ == "__main__":
    main()
