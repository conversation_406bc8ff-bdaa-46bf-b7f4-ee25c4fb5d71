#!/usr/bin/env python3
"""
Test GUI simulation with fixed nPBC handling
"""

import os
import subprocess

def parse_npbc_fixed(npbc_input):
    """Fixed nPBC parsing logic from GUI"""
    npbc_input = npbc_input.strip()
    nPBCs = []
    if npbc_input:
        # Split by "),(" to handle multiple nPBC values like "(1,1,0),(2,2,0)"
        if '),(' in npbc_input:
            parts = npbc_input.split('),(')
            for i, part in enumerate(parts):
                if i == 0:
                    part = part + ')'  # Add closing parenthesis to first part
                elif i == len(parts) - 1:
                    part = '(' + part  # Add opening parenthesis to last part
                else:
                    part = '(' + part + ')'  # Add both parentheses to middle parts
                nPBCs.append(part.strip())
        else:
            # Single nPBC value
            nPBCs.append(npbc_input)
    return nPBCs

def simulate_gui_run():
    """Simulate GUI run with fixed nPBC handling"""
    print("Simulating GUI run with fixed nPBC handling...")
    
    # Test parameters from params_no_surface.txt
    npbc_input = "(0,0,0),(1,1,0)"
    nPBCs = parse_npbc_fixed(npbc_input)
    
    print(f"Input nPBC: {npbc_input}")
    print(f"Parsed nPBCs: {nPBCs}")
    
    # Simulate running simulations for each nPBC
    os.makedirs("results/all", exist_ok=True)
    
    for i, nPBC in enumerate(nPBCs):
        print(f"\nSimulating run {i+1} with nPBC: {nPBC}")
        
        # Simulate calling run_throughput_MD.py
        cmd = [
            "python3", "run_throughput_MD.py",
            "--nSys", "1000",
            "--xyz_name", "data/xyz/molecule_no_surface",
            "--dovdW", "1",
            "--doSurfAtoms", "0",
            "--GridFF", "-1",
            "--Fconv", "1e-4",
            "--perframe", "20",
            "--perVF", "20",
            "--gridnPBC", nPBC
        ]
        
        print(f"Command: {' '.join(cmd)}")
        
        # Run simulation
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=".")
        
        if result.returncode == 0:
            print("Simulation successful!")
            
            # Generate filename with fixed nPBC handling
            nPBC_safe = nPBC.replace('(', '_').replace(')', '_').replace(',', '_')
            filename = f"minima__10000_surf:_NaCl_0x0_nPBC_{nPBC_safe}_nloc:_MMFF_32_move_32_NBFF_--_surf_--_gridFF_--_gridFFbSpline_--___replica:_1000_perframe:_20_perVF:_20"
            
            print(f"Generated filename: {filename}")
            
            # Move results file
            if os.path.exists("minima.dat"):
                os.rename("minima.dat", f"results/all/{filename}.dat")
                
                # Read last line and append to results.dat
                with open(f"results/all/{filename}.dat", 'r') as f:
                    lines = f.readlines()
                    if lines:
                        last_line = lines[-1].strip()
                        
                        # Append to results file
                        with open("results/all/results.dat", 'a') as rf:
                            rf.write(f"{filename} {last_line}\n")
                        
                        print(f"Added to results.dat: {filename}")
            else:
                print("Warning: minima.dat not found")
        else:
            print(f"Simulation failed: {result.stderr}")

def check_results():
    """Check the generated results"""
    print("\n" + "="*50)
    print("Checking generated results...")
    
    if os.path.exists("results/all/results.dat"):
        with open("results/all/results.dat", 'r') as f:
            lines = f.readlines()
            
        print(f"Found {len(lines)} entries in results.dat")
        
        # Show last few entries
        print("\nLast few entries:")
        for line in lines[-5:]:
            parts = line.strip().split()
            if parts:
                filename = parts[0]
                # Extract nPBC from filename
                if "_nPBC_" in filename:
                    npbc_part = filename.split("_nPBC_")[1].split("_nloc:")[0]
                    print(f"  nPBC: {npbc_part} -> {filename}")
    else:
        print("No results.dat found")

def main():
    print("=== Testing GUI Simulation with Fixed nPBC ===")
    
    simulate_gui_run()
    check_results()
    
    print("\n=== Test Complete ===")
    print("You can now:")
    print("1. Check results/all/results.dat for properly formatted nPBC values")
    print("2. Run: python3 plot_data.py to visualize the results")

if __name__ == "__main__":
    main()
