#!/usr/bin/env python3
"""
Generate simple clean test data with proper nPBC format
"""

import subprocess
import os
import time as time_module

def main():
    # Create results directory
    os.makedirs("results/all", exist_ok=True)
    
    results_data = []
    
    print("Generating simple clean test data...")
    print("=" * 60)
    
    # Just generate surface with GridFF data (most common scenario)
    surface_sizes = [f"NaCl_{i}x{i}" for i in range(1, 17)]
    perframe_values = [20, 500]
    perVF_values = [20, 50]
    nSys_values = [1000, 5000]
    
    total = len(surface_sizes) * len(perframe_values) * len(perVF_values) * len(nSys_values)
    current = 0
    
    for surf_name in surface_sizes:
        for nSys in nSys_values:
            for perframe in perframe_values:
                for perVF in perVF_values:
                    current += 1
                    print(f"[{current}/{total}] Processing {surf_name}, nSys={nSys}, perframe={perframe}, perVF={perVF}")
                    
                    # Run simulation
                    cmd = [
                        "python3", "run_throughput_MD.py",
                        "--nSys", str(nSys),
                        "--surf_name", surf_name,
                        "--perframe", str(perframe),
                        "--perVF", str(perVF),
                        "--gridnPBC", "(1,1,0)",
                        "--dovdW", "1",
                        "--doSurfAtoms", "1", 
                        "--GridFF", "6",
                        "--Fconv", "0.0001"
                    ]
                    
                    result = subprocess.run(cmd, capture_output=True, text=True, cwd=".")
                    
                    if result.returncode == 0 and os.path.exists("minima.dat"):
                        with open("minima.dat", 'r') as f:
                            lines = f.readlines()
                            for line in lines:
                                if line.strip() and not line.startswith('#'):
                                    parts = line.strip().split()
                                    if len(parts) >= 6:
                                        energy = parts[1]
                                        force = parts[2]
                                        time = parts[3]
                                        steps = parts[4]
                                        throughput = parts[5]
                                        
                                        # Create clean filename with proper nPBC format
                                        filename = f"minima__11110_surf:_{surf_name}_nPBC_(1,1,0)_nloc:_MMFF_32_move_32_NBFF_32_surf_32_gridFF_32_gridFFbSpline_32___replica:_{nSys}_perframe:_{perframe}_perVF:_{perVF}"
                                        
                                        result_line = f"{filename} 1 {energy} {force} {time} {steps} {throughput}"
                                        results_data.append(result_line)
                                        print(f"  → Energy: {energy}, Time: {time}s, Throughput: {throughput}")
                                        break
                        
                        # Clean up
                        if os.path.exists("minima.dat"):
                            os.remove("minima.dat")
                    else:
                        print(f"  → Simulation failed: {result.stderr}")
                    
                    time_module.sleep(0.05)
    
    # Write all results to results.dat
    results_file = "results/all/results.dat"
    with open(results_file, 'w') as f:
        for line in results_data:
            f.write(line + '\n')
    
    print("=" * 60)
    print(f"Generated {len(results_data)} data points")
    print(f"Results saved to: {results_file}")
    print("Ready for visualization!")
    
    print("\nNext steps:")
    print("1. Run: python3 throughput_gui.py")
    print("2. Go to 'Visualization' tab")
    print("3. Click 'Generate Interactive Plot'")

if __name__ == "__main__":
    main()
