#!/usr/bin/env python3
"""
Test script to verify GUI nPBC handling
"""

def test_npbc_parsing():
    """Test how GUI parses nPBC values"""
    print("Testing nPBC parsing in GUI...")
    
    # Test cases from params files
    test_cases = [
        "(0,0,0)",
        "(1,1,0)",
        "(0,0,0),(1,1,0)",
        "(1,1,0),(2,2,0),(3,3,0)",
        "(1,1,0),(2,2,0),(3,3,0),(0,0,0)"
    ]
    
    for test_input in test_cases:
        print(f"\nInput: {test_input}")
        
        # Simulate GUI parsing
        nPBCs = [x.strip() for x in test_input.split(',')]
        print(f"Parsed nPBCs: {nPBCs}")
        
        # Simulate filename generation for each nPBC
        for nPBC in nPBCs:
            nPBC_safe = nPBC.replace('(', '_').replace(')', '_').replace(',', '_')
            print(f"  {nPBC} -> {nPBC_safe}")
            
            # Generate sample filename
            filename = f"minima__11110_surf:_NaCl_4x4_nPBC_{nPBC_safe}_nloc:_MMFF_32_move_32_NBFF_32_surf_32_gridFF_32_gridFFbSpline_32___replica:_1000_perframe:_20_perVF:_20"
            print(f"  Filename: {filename}")

def test_params_loading():
    """Test loading parameters from files"""
    print("\n" + "="*50)
    print("Testing parameter loading from files...")
    
    param_files = [
        "params_no_surface.txt",
        "params_surface_with_gridff.txt", 
        "params_surface_no_gridff.txt"
    ]
    
    for filename in param_files:
        print(f"\nLoading {filename}:")
        try:
            with open(filename, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        if '=' in line:
                            key, value = line.split('=', 1)
                            key = key.strip()
                            value = value.strip()
                            
                            if key == 'nPBC':
                                print(f"  Found nPBC: {value}")
                                
                                # Test parsing
                                nPBCs = [x.strip() for x in value.split(',')]
                                print(f"  Parsed as: {nPBCs}")
                                
                                # Test escaping
                                for nPBC in nPBCs:
                                    nPBC_safe = nPBC.replace('(', '_').replace(')', '_').replace(',', '_')
                                    print(f"    {nPBC} -> {nPBC_safe}")
        except FileNotFoundError:
            print(f"  File not found: {filename}")
        except Exception as e:
            print(f"  Error: {e}")

def main():
    print("=== GUI nPBC Testing ===")
    
    test_npbc_parsing()
    test_params_loading()
    
    print("\n" + "="*50)
    print("Test complete!")
    print("\nTo test with actual GUI:")
    print("1. Load params_no_surface.txt in GUI")
    print("2. Check that nPBC field shows: (0,0,0),(1,1,0)")
    print("3. Run a small test to see if filenames are generated correctly")

if __name__ == "__main__":
    main()
