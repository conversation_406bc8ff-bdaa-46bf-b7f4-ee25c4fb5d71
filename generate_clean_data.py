#!/usr/bin/env python3
"""
Generate clean test data for three simulation scenarios:
1. No surface (doSurfAtoms=0, bGridFF=0)
2. Surface without GridFF (doSurfAtoms=1, bGridFF=0) 
3. Surface with GridFF (doSurfAtoms=1, bGridFF=6)
"""

import subprocess
import os
import time as time_module

def main():
    # Create results directory
    os.makedirs("results/all", exist_ok=True)
    
    results_data = []
    
    print("Generating clean test data for three scenarios...")
    print("=" * 60)
    
    # Scenario 1: No surface
    print("\n1. Generating data for NO SURFACE scenario...")
    scenario1_data = generate_no_surface_data()
    results_data.extend(scenario1_data)
    
    # Scenario 2: Surface without GridFF
    print("\n2. Generating data for SURFACE WITHOUT GRIDFF scenario...")
    scenario2_data = generate_surface_no_gridff_data()
    results_data.extend(scenario2_data)
    
    # Scenario 3: Surface with GridFF
    print("\n3. Generating data for SURFACE WITH GRIDFF scenario...")
    scenario3_data = generate_surface_with_gridff_data()
    results_data.extend(scenario3_data)
    
    # Write all results to results.dat
    results_file = "results/all/results.dat"
    with open(results_file, 'w') as f:
        for line in results_data:
            f.write(line + '\n')
    
    print("=" * 60)
    print(f"Generated {len(results_data)} data points")
    print(f"Results saved to: {results_file}")
    print("Ready for visualization!")

def generate_no_surface_data():
    """Generate data for simulations without surface"""
    data = []
    perframe_values = [20, 500]
    perVF_values = [20, 50]
    nSys_values = [1000, 5000]
    
    for nSys in nSys_values:
        for perframe in perframe_values:
            for perVF in perVF_values:
                # Run simulation
                cmd = [
                    "python3", "run_throughput_MD.py",
                    "--nSys", str(nSys),
                    "--perframe", str(perframe),
                    "--perVF", str(perVF),
                    "--gridnPBC", "(0,0,0)",
                    "--dovdW", "1",
                    "--doSurfAtoms", "0", 
                    "--GridFF", "0",
                    "--Fconv", "0.0001"
                ]
                
                result = subprocess.run(cmd, capture_output=True, text=True, cwd=".")
                
                if result.returncode == 0 and os.path.exists("minima.dat"):
                    with open("minima.dat", 'r') as f:
                        lines = f.readlines()
                        for line in lines:
                            if line.strip() and not line.startswith('#'):
                                parts = line.strip().split()
                                if len(parts) >= 6:
                                    energy = parts[1]
                                    force = parts[2]
                                    time = parts[3]
                                    steps = parts[4]
                                    throughput = parts[5]
                                    
                                    # Create filename for no surface scenario
                                    filename = f"minima__10000_surf:_no_surface_nPBC_(0,0,0)_nloc:_MMFF_32_move_32_NBFF_--_surf_--_gridFF_--_gridFFbSpline_--___replica:_{nSys}_perframe:_{perframe}_perVF:_{perVF}"
                                    
                                    result_line = f"{filename} 1 {energy} {force} {time} {steps} {throughput}"
                                    data.append(result_line)
                                    print(f"  → No surface: nSys={nSys}, perframe={perframe}, perVF={perVF}")
                                    break
                    
                    # Clean up
                    if os.path.exists("minima.dat"):
                        os.remove("minima.dat")
                
                time_module.sleep(0.1)
    
    return data

def generate_surface_no_gridff_data():
    """Generate data for simulations with surface but without GridFF"""
    data = []
    surface_sizes = [f"NaCl_{i}x{i}" for i in range(1, 17)]
    perframe_values = [20, 500]
    perVF_values = [20, 50]
    nSys_values = [1000, 5000]
    
    for surf_name in surface_sizes:
        for nSys in nSys_values:
            for perframe in perframe_values:
                for perVF in perVF_values:
                    # Run simulation
                    cmd = [
                        "python3", "run_throughput_MD.py",
                        "--nSys", str(nSys),
                        "--surf_name", surf_name,
                        "--perframe", str(perframe),
                        "--perVF", str(perVF),
                        "--gridnPBC", "(1,1,0)",
                        "--dovdW", "1",
                        "--doSurfAtoms", "1", 
                        "--GridFF", "0",
                        "--Fconv", "0.0001"
                    ]
                    
                    result = subprocess.run(cmd, capture_output=True, text=True, cwd=".")
                    
                    if result.returncode == 0 and os.path.exists("minima.dat"):
                        with open("minima.dat", 'r') as f:
                            lines = f.readlines()
                            for line in lines:
                                if line.strip() and not line.startswith('#'):
                                    parts = line.strip().split()
                                    if len(parts) >= 6:
                                        energy = parts[1]
                                        force = parts[2]
                                        time = parts[3]
                                        steps = parts[4]
                                        throughput = parts[5]
                                        
                                        # Create filename for surface without GridFF
                                        filename = f"minima__11000_surf:_{surf_name}_nPBC_(1,1,0)_nloc:_MMFF_32_move_32_NBFF_32_surf_32_gridFF_--_gridFFbSpline_--___replica:_{nSys}_perframe:_{perframe}_perVF:_{perVF}"
                                        
                                        result_line = f"{filename} 1 {energy} {force} {time} {steps} {throughput}"
                                        data.append(result_line)
                                        print(f"  → Surface no GridFF: {surf_name}, nSys={nSys}, perframe={perframe}, perVF={perVF}")
                                        break
                        
                        # Clean up
                        if os.path.exists("minima.dat"):
                            os.remove("minima.dat")
                    
                    time_module.sleep(0.05)
    
    return data

def generate_surface_with_gridff_data():
    """Generate data for simulations with surface and GridFF"""
    data = []
    surface_sizes = [f"NaCl_{i}x{i}" for i in range(1, 17)]
    perframe_values = [20, 500]
    perVF_values = [20, 50]
    nSys_values = [1000, 5000]
    
    for surf_name in surface_sizes:
        for nSys in nSys_values:
            for perframe in perframe_values:
                for perVF in perVF_values:
                    # Run simulation
                    cmd = [
                        "python3", "run_throughput_MD.py",
                        "--nSys", str(nSys),
                        "--surf_name", surf_name,
                        "--perframe", str(perframe),
                        "--perVF", str(perVF),
                        "--gridnPBC", "(1,1,0)",
                        "--dovdW", "1",
                        "--doSurfAtoms", "1", 
                        "--GridFF", "6",
                        "--Fconv", "0.0001"
                    ]
                    
                    result = subprocess.run(cmd, capture_output=True, text=True, cwd=".")
                    
                    if result.returncode == 0 and os.path.exists("minima.dat"):
                        with open("minima.dat", 'r') as f:
                            lines = f.readlines()
                            for line in lines:
                                if line.strip() and not line.startswith('#'):
                                    parts = line.strip().split()
                                    if len(parts) >= 6:
                                        energy = parts[1]
                                        force = parts[2]
                                        time = parts[3]
                                        steps = parts[4]
                                        throughput = parts[5]
                                        
                                        # Create filename for surface with GridFF
                                        filename = f"minima__11110_surf:_{surf_name}_nPBC_(1,1,0)_nloc:_MMFF_32_move_32_NBFF_32_surf_32_gridFF_32_gridFFbSpline_32___replica:_{nSys}_perframe:_{perframe}_perVF:_{perVF}"
                                        
                                        result_line = f"{filename} 1 {energy} {force} {time} {steps} {throughput}"
                                        data.append(result_line)
                                        print(f"  → Surface with GridFF: {surf_name}, nSys={nSys}, perframe={perframe}, perVF={perVF}")
                                        break
                        
                        # Clean up
                        if os.path.exists("minima.dat"):
                            os.remove("minima.dat")
                    
                    time_module.sleep(0.05)
    
    return data

if __name__ == "__main__":
    main()
