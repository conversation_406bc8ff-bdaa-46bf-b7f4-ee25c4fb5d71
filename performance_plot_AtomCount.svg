<svg class="main-svg" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="700" height="700" style="" viewBox="0 0 700 700"><rect x="0" y="0" width="700" height="700" style="fill: rgb(255, 255, 255); fill-opacity: 1;"/><defs id="defs-518829"><g class="clips"><clipPath id="clip518829xyplot" class="plotclip"><rect width="540" height="500"/></clipPath><clipPath class="axesclip" id="clip518829x"><rect x="80" y="0" width="540" height="700"/></clipPath><clipPath class="axesclip" id="clip518829y"><rect x="0" y="100" width="700" height="500"/></clipPath><clipPath class="axesclip" id="clip518829xy"><rect x="80" y="100" width="540" height="500"/></clipPath></g><g class="gradients"/><g class="patterns"/></defs><g class="bglayer"><rect class="bg" x="80" y="100" width="540" height="500" style="fill: rgb(240, 248, 255); fill-opacity: 0.95; stroke-width: 0;"/></g><g class="layer-below"><g class="imagelayer"/><g class="shapelayer"/></g><g class="cartesianlayer"><g class="subplot xy"><g class="layer-subplot"><g class="shapelayer"/><g class="imagelayer"/></g><g class="minor-gridlayer"><g class="x"/><g class="y"/></g><g class="gridlayer"><g class="x"><path class="xgrid crisp" transform="translate(239.99,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(365.48,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(490.97,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(616.47,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g><g class="y"><path class="ygrid crisp" transform="translate(0,506.9)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,446.08)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,385.25)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,324.43)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,263.6)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,202.78)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,141.96)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g></g><g class="zerolinelayer"><path class="xzl zl crisp" transform="translate(114.5,0)" d="M0,100v500" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;"/><path class="yzl zl crisp" transform="translate(0,567.72)" d="M80,0h540" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;"/></g><g class="layer-between"><g class="shapelayer"/><g class="imagelayer"/></g><path class="xlines-below"/><path class="ylines-below"/><g class="overlines-below"/><g class="xaxislayer-below"/><g class="yaxislayer-below"/><g class="overaxes-below"/><g class="overplot"><g class="xy" transform="translate(80,100)" clip-path="url(#clip518829xyplot)"><g class="scatterlayer mlayer"><g class="trace scatter trace68544e" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,224.55" style="vector-effect: none; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,224.55)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace1d52fc" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,218.53" style="vector-effect: none; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,218.53)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace6fcbb9" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,252.65" style="vector-effect: none; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,252.65)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace770fee" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,64.52" style="vector-effect: none; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,64.52)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter traced520f4" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,60.51" style="vector-effect: none; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,60.51)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace2e8154" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,150.04" style="vector-effect: none; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,150.04)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace94813c" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,152.17" style="vector-effect: none; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,152.17)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace44ff33" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,163.72" style="vector-effect: none; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,163.72)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter tracea22b2f" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,189.39" style="vector-effect: none; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,189.39)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace14670c" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,108.62" style="vector-effect: none; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,108.62)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace5b34f0" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,62.15" style="vector-effect: none; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,62.15)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace3158e1" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,124.49" style="vector-effect: none; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,124.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace1e8c07" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,180.88" style="vector-effect: none; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,180.88)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace765948" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,206.24" style="vector-effect: none; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,206.24)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace70a7fd" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,168.89" style="vector-effect: none; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,168.89)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter traceead367" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,56.67" style="vector-effect: none; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,56.67)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace367797" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,144.02" style="vector-effect: none; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,144.02)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter tracec9029a" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.5,68.72" style="vector-effect: none; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.5,68.72)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace30778a" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.54,203.63L34.54,180.63L35.1,456.08L35.44,461.3L35.44,459.82L35.86,464.36L35.86,463.77L36.34,465.59L36.34,465.1L36.91,466.44L36.91,466.31L37.55,466.5L38.26,466.94L38.26,466.92L39.06,467.17L39.06,467.02L39.92,467.35L42.97,467.41L44.14,467.56" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.54,203.63)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.54,205.94)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.54,180.63)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.65,365.56)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.65,369.09)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.65,386.43)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.84,441.64)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.84,430.66)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.84,437.83)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.1,454.26)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.1,451.82)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.1,456.08)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.44,461.3)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.44,459.82)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.44,460.33)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,463.76)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,464.36)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,463.77)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.34,465.59)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.34,465.1)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.34,465.59)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.91,465.85)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.91,466.44)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.91,466.31)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,466.5)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,466.39)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,466.66)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,466.74)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,466.94)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,466.92)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.06,467.17)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.06,467.1)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.06,467.02)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,467.35)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,467.32)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,467.29)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.86,467.21)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.86,467.31)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.86,467.31)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,467.31)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,467.37)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,467.36)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,467.51)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,467.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,467.41)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(44.14,467.56)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(44.14,467.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(44.14,467.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace262aeb" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.54,189.27L34.54,181.24L35.1,459.24L35.44,462.65L35.44,461.86L35.86,465.06L35.86,464.48L36.34,465.43L36.34,465.28L36.91,466.4L37.55,466.88L42.97,467.58L42.97,467.52L44.14,467.53" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.54,189.27)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.54,183.98)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.54,181.24)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.65,376.63)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.65,369.29)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.65,375.09)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.84,442.92)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.84,437.54)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.84,443.78)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.1,456.54)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.1,454.66)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.1,459.24)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.44,462.65)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.44,461.86)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.44,463.07)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,465.06)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,464.58)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,464.48)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.34,465.43)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.34,465.87)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.34,465.28)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.91,466.29)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.91,466.32)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.91,466.4)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,466.88)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,466.8)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,466.88)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,467.02)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,467.21)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,467.12)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.06,467.09)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.06,467.31)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.06,467.08)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,467.34)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,467.3)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,467.27)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.86,467.48)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.86,467.34)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.86,467.37)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,467.5)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,467.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,467.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,467.51)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,467.58)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,467.52)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(44.14,467.53)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(44.14,467.59)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(44.14,467.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter tracef0255c" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.54,175.04L35.1,458.84L35.44,462.29L35.86,465.23L36.34,465.94L36.34,465.72L36.91,466.37L36.91,466.14L37.55,466.83L37.55,466.55L38.26,467.01L38.26,467.01L39.06,467.16L44.14,467.57L44.14,467.57" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.54,175.04)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.54,201.56)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.54,180.88)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.65,362.46)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.65,379.02)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.65,394.44)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.84,441.72)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.84,442.92)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.84,443.17)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.1,455.46)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.1,457.17)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.1,458.84)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.44,462.29)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.44,463.07)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.44,462.9)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,464.84)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,464.24)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,465.23)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.34,465.94)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.34,465.76)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.34,465.72)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.91,466.37)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.91,466.29)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.91,466.14)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,466.83)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,466.68)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,466.55)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,466.96)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,467.01)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,467.01)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.06,467.16)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.06,467.31)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.06,467.07)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,467.31)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,467.37)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,467.26)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.86,467.4)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.86,467.35)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.86,467.38)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,467.47)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,467.45)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,467.46)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,467.52)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,467.52)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,467.57)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(44.14,467.57)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(44.14,467.54)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(44.14,467.57)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace5802bc" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.54,59.59L34.54,58.56L35.1,452.76L35.44,456.72L35.86,462.63L35.86,462.09L36.34,463.42L36.91,465.71L36.91,465.4L37.55,466.06L39.92,467.16L40.86,467.11L44.14,467.5" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.54,59.59)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.54,58.56)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.54,110.81)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.65,341.23)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.65,322.29)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.65,285.41)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.84,428.16)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.84,429.88)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.84,420.73)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.1,448.73)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.1,445.98)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.1,452.76)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.44,456.72)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.44,457.34)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.44,461.02)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,462.31)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,462.63)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,462.09)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.34,463.42)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.34,464.46)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.34,464.64)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.91,465.71)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.91,464.86)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.91,465.4)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,466.06)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,466.2)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,466.1)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,466.47)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,466.61)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,466.69)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.06,466.93)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.06,467.02)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.06,466.83)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,466.93)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,466.86)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,467.16)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.86,467.11)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.86,467.21)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.86,467.21)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,467.25)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,467.25)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,467.3)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,467.39)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,467.3)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,467.34)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(44.14,467.49)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(44.14,467.39)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(44.14,467.5)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter tracec5dfd5" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.54,43.72L34.54,38.18L35.1,456.14L35.1,455.32L35.44,461.32L35.44,458.41L35.86,464.39L35.86,463.55L36.34,464.93L36.34,464.73L36.91,465.91L37.55,466.58L44.14,467.51" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.54,43.72)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.54,38.18)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.54,141.71)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.65,316.44)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.65,375.04)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.65,375.31)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.84,432.19)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.84,438.41)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.84,437.98)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.1,456.14)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.1,450.22)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.1,455.32)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.44,461.32)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.44,458.41)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.44,459.29)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,464.39)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,463)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,463.55)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.34,464.93)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.34,464.73)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.34,465.18)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.91,465.81)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.91,465.78)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.91,465.91)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,466.58)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,466.54)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,466.59)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,466.63)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,466.57)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,466.95)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.06,467.08)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.06,467.07)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.06,467.12)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,467.09)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,467.24)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,467.05)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.86,467.27)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.86,467.31)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.86,467.32)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,467.3)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,467.29)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,467.38)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,467.47)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,467.46)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,467.38)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(44.14,467.5)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(44.14,467.47)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(44.14,467.51)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace9709c0" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.54,110.81L34.54,90.19L35.1,453.98L35.1,451.67L35.44,460.6L35.44,458.86L35.86,462.86L36.34,464.86L36.91,466.26L36.91,466.2L37.55,466.2L39.92,467.27L39.92,467.21L40.86,467.28L44.14,467.5L44.14,467.49" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.54,110.81)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.54,90.19)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.54,173.76)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.65,342.68)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.65,357.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.65,325.39)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.84,435.59)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.84,428.55)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.84,434.14)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.1,452.08)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.1,453.98)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.1,451.67)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.44,460.6)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.44,461.51)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.44,458.86)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,462.66)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,462.38)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,462.86)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.34,464.86)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.34,464.76)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.34,465.15)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.91,466.26)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.91,466.15)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.91,466.2)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,466.2)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,466.11)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,466.31)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,466.57)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,466.72)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,466.89)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.06,467.01)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.06,467.1)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.06,467.03)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,467.27)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,467.12)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,467.21)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.86,467.28)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.86,467.2)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.86,467.3)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,467.28)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,467.44)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,467.43)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,467.5)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,467.41)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,467.48)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(44.14,467.5)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(44.14,467.51)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(44.14,467.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter tracedc436a" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.84,190.61L35.86,374.94L37.55,437.52L39.92,456.86L42.97,462.07L46.7,464.71L51.1,465.29L61.95,466.57L68.38,466.73L83.29,467.2L91.76,467.4L121.24,467.52" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.84,190.61)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,374.94)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,437.52)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,456.86)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,462.07)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(46.7,464.71)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(51.1,465.29)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(56.18,465.97)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(61.95,466.57)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(68.38,466.73)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(75.5,467.24)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(83.29,467.2)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(91.76,467.4)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(100.91,467.45)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(110.74,467.42)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(121.24,467.52)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter tracefffc27" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.84,249.85L35.86,390.96L37.55,440.08L39.92,455.07L42.97,463.35L51.1,465.77L56.18,466.31L75.5,467.1L83.29,467.37L121.24,467.57" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.84,249.85)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,390.96)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,440.08)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,455.07)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,463.35)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(46.7,464.45)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(51.1,465.77)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(56.18,466.31)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(61.95,466.72)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(68.38,466.8)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(75.5,467.1)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(83.29,467.37)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(91.76,467.36)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(100.91,467.48)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(110.74,467.52)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(121.24,467.57)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace73e5bb" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.84,251.25L35.86,379.37L37.55,437.96L39.92,458.64L42.97,460.64L46.7,464.64L51.1,465.99L75.5,467.23L83.29,467.32L121.24,467.54" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.84,251.25)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,379.37)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,437.96)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,458.64)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,460.64)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(46.7,464.64)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(51.1,465.99)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(56.18,466.31)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(61.95,466.87)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(68.38,466.92)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(75.5,467.23)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(83.29,467.32)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(91.76,467.47)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(100.91,467.52)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(110.74,467.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(121.24,467.54)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter traceaca570" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.84,137.02L35.86,309.67L37.55,425.69L39.92,444.6L42.97,459.22L46.7,462.27L51.1,463.55L61.95,466.16L68.38,466.36L100.91,467.26L110.74,467.43L121.24,467.48" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.84,137.02)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,309.67)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,425.69)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,444.6)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,459.22)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(46.7,462.27)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(51.1,463.55)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(56.18,464.66)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(61.95,466.16)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(68.38,466.36)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(75.5,466.71)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(83.29,466.92)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(91.76,467.13)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(100.91,467.26)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(110.74,467.43)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(121.24,467.48)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace5cf043" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.84,155.39L35.86,381.64L37.55,434.22L39.92,451.15L42.97,460.55L46.7,463.96L51.1,464.49L56.18,466.41L61.95,465.92L68.38,466.84L75.5,466.81L100.91,467.4L110.74,467.39L121.24,467.55" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.84,155.39)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,381.64)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,434.22)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,451.15)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,460.55)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(46.7,463.96)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(51.1,464.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(56.18,466.41)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(61.95,465.92)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(68.38,466.84)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(75.5,466.81)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(83.29,467.12)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(91.76,467.27)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(100.91,467.4)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(110.74,467.39)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(121.24,467.55)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace184739" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.84,41.96L35.86,360.22L37.55,434.17L39.92,451.75L42.97,459.81L46.7,463.79L51.1,465.04L56.18,466.06L61.95,466.41L75.5,466.85L83.29,467.14L121.24,467.5" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.84,41.96)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,360.22)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,434.17)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,451.75)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,459.81)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(46.7,463.79)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(51.1,465.04)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(56.18,466.06)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(61.95,466.41)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(68.38,466.86)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(75.5,466.85)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(83.29,467.14)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(91.76,467.32)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(100.91,467.43)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(110.74,467.45)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(121.24,467.5)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace12c56c" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M35.44,217.49L38.26,348.28L42.97,432.26L49.56,452.68L58.03,462.21L68.38,464.16L80.62,465.3L94.74,466.26L110.74,466.29L170.03,467.24L193.56,467.24L275.44,467.5" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(35.44,217.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,348.28)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,432.26)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(49.56,452.68)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(58.03,462.21)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(68.38,464.16)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(80.62,465.3)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(94.74,466.26)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(110.74,466.29)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(128.62,466.73)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(148.38,467.15)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(170.03,467.24)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(193.56,467.24)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(218.97,467.37)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(246.27,467.42)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(275.44,467.5)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter tracefcdbae" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M35.44,182.7L38.26,392.68L42.97,443.86L49.56,457.96L58.03,462.23L80.62,465.77L94.74,466.29L148.38,467.2L170.03,467.35L275.44,467.59" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(35.44,182.7)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,392.68)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,443.86)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(49.56,457.96)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(58.03,462.23)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(68.38,463.69)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(80.62,465.77)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(94.74,466.29)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(110.74,466.7)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(128.62,466.91)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(148.38,467.2)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(170.03,467.35)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(193.56,467.47)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(218.97,467.45)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(246.27,467.55)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(275.44,467.59)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter tracec9a262" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M35.44,217.31L38.26,380.79L42.97,443.76L49.56,455.87L58.03,463.44L68.38,465.43L80.62,465.5L94.74,466.6L110.74,466.69L148.38,467.27L170.03,467.4L275.44,467.57" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(35.44,217.31)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,380.79)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,443.76)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(49.56,455.87)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(58.03,463.44)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(68.38,465.43)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(80.62,465.5)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(94.74,466.6)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(110.74,466.69)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(128.62,467.16)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(148.38,467.27)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(170.03,467.4)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(193.56,467.43)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(218.97,467.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(246.27,467.48)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(275.44,467.57)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace8e5e2d" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M35.44,158.86L38.26,320.42L42.97,417.21L49.56,447.72L58.03,457.55L68.38,462.68L80.62,464.43L110.74,466.15L128.62,466.5L275.44,467.46" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(35.44,158.86)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,320.42)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,417.21)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(49.56,447.72)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(58.03,457.55)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(68.38,462.68)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(80.62,464.43)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(94.74,465.4)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(110.74,466.15)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(128.62,466.5)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(148.38,466.7)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(170.03,467.14)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(193.56,467.22)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(218.97,467.28)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(246.27,467.3)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(275.44,467.46)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace589588" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M35.44,88.36L38.26,351.23L42.97,443.37L49.56,457.32L58.03,460.18L68.38,463.82L80.62,464.86L94.74,465.28L110.74,466.72L275.44,467.53" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(35.44,88.36)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,351.23)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,443.37)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(49.56,457.32)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(58.03,460.18)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(68.38,463.82)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(80.62,464.86)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(94.74,465.28)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(110.74,466.72)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(128.62,466.79)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(148.38,466.85)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(170.03,467.22)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(193.56,467.36)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(218.97,467.4)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(246.27,467.47)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(275.44,467.53)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace49bf8b" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M35.44,34.41L38.26,341.2L42.97,421.04L49.56,447.82L58.03,461L68.38,463.4L80.62,464.91L110.74,466.33L128.62,466.84L275.44,467.53" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(35.44,34.41)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,341.2)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,421.04)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(49.56,447.82)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(58.03,461)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(68.38,463.4)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(80.62,464.91)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(94.74,465.84)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(110.74,466.33)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(128.62,466.84)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(148.38,467.01)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(170.03,467.21)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(193.56,467.32)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(218.97,467.28)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(246.27,467.44)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(275.44,467.53)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter tracee8050d" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M36.34,174.79L41.88,382.62L51.1,437.68L64.02,456.24L80.62,460.95L100.91,463.64L124.89,465.51L218.97,467.02L257.71,467.25L506.75,467.49" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(36.34,174.79)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,382.62)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(51.1,437.68)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(64.02,456.24)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(80.62,460.95)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(100.91,463.64)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(124.89,465.51)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(152.56,466.01)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(183.92,466.69)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(218.97,467.02)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(257.71,467.25)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(300.14,467.2)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(346.26,467.27)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(396.07,467.46)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(449.56,467.47)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(506.75,467.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace50b41e" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M36.34,161.11L41.88,402.12L51.1,449L64.02,456.61L80.62,460.98L100.91,464.58L124.89,465.39L218.97,467.16L257.71,467.24L506.75,467.55" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(36.34,161.11)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,402.12)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(51.1,449)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(64.02,456.61)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(80.62,460.98)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(100.91,464.58)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(124.89,465.39)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(152.56,466.04)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(183.92,466.72)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(218.97,467.16)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(257.71,467.24)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(300.14,467.29)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(346.26,467.46)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(396.07,467.47)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(449.56,467.56)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(506.75,467.55)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace9b0d05" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M36.34,213.11L41.88,394.22L51.1,434.79L64.02,458.07L80.62,462.4L100.91,464.37L124.89,465.76L183.92,466.81L218.97,467.18L506.75,467.59" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(36.34,213.11)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,394.22)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(51.1,434.79)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(64.02,458.07)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(80.62,462.4)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(100.91,464.37)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(124.89,465.76)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(152.56,466.55)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(183.92,466.81)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(218.97,467.18)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(257.71,467.23)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(300.14,467.43)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(346.26,467.44)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(396.07,467.43)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(449.56,467.51)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(506.75,467.59)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace9f61fe" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M36.34,57.77L41.88,315.12L51.1,431.77L80.62,457.32L100.91,462.53L152.56,465.48L183.92,466.39L449.56,467.46L506.75,467.45" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(36.34,57.77)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,315.12)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(51.1,431.77)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(64.02,442.83)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(80.62,457.32)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(100.91,462.53)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(124.89,463.95)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(152.56,465.48)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(183.92,466.39)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(218.97,466.62)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(257.71,466.78)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(300.14,466.94)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(346.26,467.16)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(396.07,467.27)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(449.56,467.46)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(506.75,467.45)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter tracec759d6" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M36.34,38.97L41.88,355.35L51.1,428.79L64.02,452.68L80.62,459.12L100.91,463.68L124.89,465.18L183.92,466.57L218.97,466.63L346.26,467.22L396.07,467.26L506.75,467.45" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(36.34,38.97)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,355.35)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(51.1,428.79)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(64.02,452.68)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(80.62,459.12)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(100.91,463.68)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(124.89,465.18)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(152.56,465.98)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(183.92,466.57)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(218.97,466.63)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(257.71,467.03)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(300.14,467.14)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(346.26,467.22)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(396.07,467.26)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(449.56,467.44)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(506.75,467.45)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace423f5b" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M36.34,155.15L41.88,376.08L51.1,426.84L64.02,450.07L80.62,458.33L100.91,462.57L124.89,465.53L152.56,466.24L183.92,466.11L300.14,467.24L346.26,467.33L506.75,467.52" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(36.34,155.15)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,376.08)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(51.1,426.84)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(64.02,450.07)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(80.62,458.33)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(100.91,462.57)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(124.89,465.53)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(152.56,466.24)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(183.92,466.11)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(218.97,466.67)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(257.71,467.08)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(300.14,467.24)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(346.26,467.33)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(396.07,467.3)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(449.56,467.4)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(506.75,467.52)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter traced4638b" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.54,149.74L35.44,458.53L35.86,464.46L37.55,466.68L38.26,466.73L41.88,467.41L42.97,467.49L44.14,467.53" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.54,149.74)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.65,370.83)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.84,440.97)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.1,457.11)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.44,458.53)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,464.46)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.34,465.11)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.91,466.19)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,466.68)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,466.73)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.06,466.98)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,467.33)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.86,467.25)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,467.41)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,467.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(44.14,467.53)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace672e1f" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.54,147L35.1,458.5L35.44,462.29L35.86,465.17L36.34,465.07L36.91,466.45L39.06,467.27L39.92,467.34L44.14,467.53" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.54,147)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.65,387.4)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.84,444.06)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.1,458.5)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.44,462.29)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,465.17)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.34,465.07)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.91,466.45)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,466.83)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,467.12)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.06,467.27)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,467.34)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.86,467.5)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,467.52)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,467.58)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(44.14,467.53)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace82677f" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.54,173.52L35.1,453.97L35.44,461.15L36.34,465.79L36.91,466.64L41.88,467.53L42.97,467.5L44.14,467.57" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.54,173.52)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.65,385.46)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.84,440.68)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.1,453.97)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.44,461.15)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,464.71)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.34,465.79)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.91,466.64)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,466.85)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,467.1)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.06,467.28)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,467.41)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.86,467.47)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,467.53)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,467.5)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(44.14,467.57)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter tracebe9b44" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.54,52.17L35.44,458.06L35.86,461.48L36.91,465.24L37.55,466.24L39.06,466.88L39.92,467L44.14,467.47" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.54,52.17)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.65,323.29)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.84,423.31)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.1,451.72)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.44,458.06)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,461.48)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.34,464.44)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.91,465.24)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,466.24)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,466.75)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.06,466.88)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,467)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.86,467.24)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,467.23)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,467.45)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(44.14,467.47)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter tracefb716e" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.54,57.16L35.44,461.3L35.86,462.84L36.91,465.65L37.55,466.43L39.92,467.3L40.86,467.29L44.14,467.49" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.54,57.16)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.65,326.5)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.84,435.51)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.1,454.41)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.44,461.3)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,462.84)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.34,465.45)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.91,465.65)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,466.43)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,466.94)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.06,467.07)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,467.3)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.86,467.29)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,467.39)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,467.41)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(44.14,467.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter tracebb39d6" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.54,108.19L35.44,459.63L35.86,464.13L37.55,466.31L38.26,467.01L44.14,467.53" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.54,108.19)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.65,330.01)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.84,442.12)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.1,451.35)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.44,459.63)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,464.13)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.34,465.18)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.91,465.97)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,466.31)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,467.01)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.06,466.99)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,467.32)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.86,467.31)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,467.33)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,467.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(44.14,467.53)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter tracee4c40f" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.54,190.79L35.44,459.82L35.86,463.99L37.55,466.66L38.26,466.93L42.97,467.43L44.14,467.52" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.54,190.79)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.65,366.99)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.84,437.08)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.1,453.88)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.44,459.82)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,463.99)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.34,464.99)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.91,465.78)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,466.66)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,466.93)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.06,467.13)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,467.28)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.86,467.39)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,467.33)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,467.43)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(44.14,467.52)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace9e86c1" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.54,240.55L35.44,462.32L35.86,464.78L37.55,466.92L38.26,466.98L40.86,467.43L41.88,467.46L44.14,467.53" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.54,240.55)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.65,394.24)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.84,446.86)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.1,457.85)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.44,462.32)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,464.78)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.34,465.83)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.91,466.39)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,466.92)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,466.98)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.06,467.29)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,467.37)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.86,467.43)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,467.46)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,467.51)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(44.14,467.53)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace52160e" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.54,192.8L35.44,462.7L35.86,464.32L36.91,466.41L37.55,466.63L39.92,467.41L40.86,467.44L44.14,467.56" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.54,192.8)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.65,382.72)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.84,440.65)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.1,459.43)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.44,462.7)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,464.32)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.34,466.31)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.91,466.41)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,466.63)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,467.02)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.06,467.19)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,467.41)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.86,467.44)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,467.44)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,467.55)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(44.14,467.56)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter traceb95b80" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.54,31.25L35.44,458.74L35.86,463.19L37.55,466.24L38.26,466.55L39.92,467.06L40.86,467.15L44.14,467.46" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.54,31.25)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.65,328.32)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.84,408.27)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.1,447.99)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.44,458.74)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,463.19)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.34,464.7)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.91,465.64)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,466.24)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,466.55)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.06,467.03)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,467.06)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.86,467.15)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,467.27)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,467.33)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(44.14,467.46)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace8a6054" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.54,132.04L35.44,461.11L35.86,462.29L36.91,465.79L37.55,466.46L39.92,467.03L40.86,467.28L44.14,467.46" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.54,132.04)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.65,371.05)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.84,430.54)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.1,452.21)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.44,461.11)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,462.29)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.34,464.87)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.91,465.79)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,466.46)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,466.84)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.06,467.17)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,467.03)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.86,467.28)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,467.25)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,467.46)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(44.14,467.46)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter tracec60a6d" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.54,68.05L35.1,453.26L35.44,458.51L36.34,464.76L36.91,465.44L38.26,466.71L39.06,466.94L44.14,467.46" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.54,68.05)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.65,374.73)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.84,440.01)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.1,453.26)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.44,458.51)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,463.94)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.34,464.76)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.91,465.44)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,466.66)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,466.71)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.06,466.94)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,467.15)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.86,467.3)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,467.41)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,467.51)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(44.14,467.46)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter tracedf767c" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.54,233L35.44,462.27L35.86,464.12L37.55,466.49L38.26,467.03L40.86,467.34L41.88,467.46L44.14,467.53" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.54,233)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.65,355.61)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.84,440.56)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.1,455.41)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.44,462.27)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,464.12)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.34,465.27)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.91,466.21)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,466.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,467.03)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.06,466.92)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,467.33)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.86,467.34)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,467.46)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,467.47)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(44.14,467.53)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace0c3390" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.54,250.82L35.44,461.95L35.86,464.42L37.55,467.06L38.26,467.01L40.86,467.42L41.88,467.47L44.14,467.6" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.54,250.82)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.65,368.84)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.84,435.61)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.1,457.39)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.44,461.95)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,464.42)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.34,465.56)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.91,466.07)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,467.06)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,467.01)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.06,467.3)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,467.39)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.86,467.42)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,467.47)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,467.5)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(44.14,467.6)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace917bc9" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.54,188.54L35.44,462.02L35.86,464.88L36.91,466.1L37.55,466.78L42.97,467.53L44.14,467.58" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.54,188.54)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.65,383.79)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.84,440.99)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.1,456.63)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.44,462.02)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,464.88)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.34,466.12)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.91,466.1)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,466.78)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,466.97)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.06,467.25)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,467.33)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.86,467.31)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,467.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,467.53)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(44.14,467.58)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace119d0a" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.54,156.73L35.44,456.8L35.86,462.02L37.55,466L38.26,466.21L39.92,467.12L40.86,467.28L44.14,467.45" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.54,156.73)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.65,337.91)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.84,435.82)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.1,450.97)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.44,456.8)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,462.02)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.34,463.43)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.91,465.46)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,466)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,466.21)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.06,466.91)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,467.12)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.86,467.28)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,467.34)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,467.42)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(44.14,467.45)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace95e6fd" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.54,80.58L35.44,460.27L35.86,463.43L38.26,466.81L39.06,466.85L42.97,467.5L44.14,467.49" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.54,80.58)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.65,340.32)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.84,425.35)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.1,452.11)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.44,460.27)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,463.43)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.34,464.28)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.91,465.4)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,466.57)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,466.81)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.06,466.85)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,467.11)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.86,467.35)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,467.35)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,467.5)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(44.14,467.49)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter tracecdab1e" style="stroke-miterlimit: 2; opacity: 0.3;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M34.54,174.92L35.44,460.27L35.86,464.03L37.55,466.33L38.26,466.68L40.86,467.28L41.88,467.28L44.14,467.47" style="vector-effect: none; fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(34.54,174.92)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.65,345.13)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(34.84,431.15)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.1,452.16)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.44,460.27)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(35.86,464.03)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.34,465.14)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(36.91,465.91)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(37.55,466.33)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(38.26,466.68)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.06,467)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(39.92,467.23)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(40.86,467.28)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(41.88,467.28)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(42.97,467.4)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/><path class="point" transform="translate(44.14,467.47)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g><g class="text"/></g></g></g></g><path class="xlines-above crisp" d="M0,0" style="fill: none;"/><path class="ylines-above crisp" d="M0,0" style="fill: none;"/><g class="overlines-above"/><g class="xaxislayer-above"><g class="xtick"><text text-anchor="middle" x="0" y="615" transform="translate(114.5,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">0</text></g><g class="xtick"><text text-anchor="middle" x="0" y="615" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(239.99,0)">20k</text></g><g class="xtick"><text text-anchor="middle" x="0" y="615" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(365.48,0)">40k</text></g><g class="xtick"><text text-anchor="middle" x="0" y="615" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(490.97,0)">60k</text></g><g class="xtick"><text text-anchor="middle" x="0" y="615" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(616.47,0)">80k</text></g></g><g class="yaxislayer-above"><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" transform="translate(0,567.72)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">0</text></g><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,506.9)">100</text></g><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,446.08)">200</text></g><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,385.25)">300</text></g><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,324.43)">400</text></g><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,263.6)">500</text></g><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,202.78)">600</text></g><g class="ytick"><text text-anchor="end" x="79" y="4.8999999999999995" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,141.96)">700</text></g></g><g class="overaxes-above"/></g></g><g class="polarlayer"/><g class="smithlayer"/><g class="ternarylayer"/><g class="geolayer"/><g class="funnelarealayer"/><g class="pielayer"/><g class="iciclelayer"/><g class="treemaplayer"/><g class="sunburstlayer"/><g class="glimages"/><defs id="topdefs-518829"><g class="clips"/><clipPath id="legend518829"><rect width="259" height="32" x="0" y="0"/></clipPath></defs><g class="layer-above"><g class="imagelayer"/><g class="shapelayer"/></g><g class="infolayer"><g class="legend" pointer-events="all" transform="translate(361,57.999999999999986)"><rect class="bg" shape-rendering="crispEdges" width="259" height="32" x="0" y="0" style="stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;"/><g class="scrollbox" transform="" clip-path="url(#legend518829)"><g class="groups"><g class="traces" transform="translate(0,15.6)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="5.46" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Without surface</text><g class="layers" style="opacity: 0.3;"><g class="legendfill"/><g class="legendlines"><path class="js-line" d="M5,0h30" style="fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px;"/></g><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(255, 0, 0); fill-opacity: 1; stroke: rgb(255, 0, 0); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-10.6" width="146.359375" height="21.2" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(148.859375,15.6)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="5.46" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">No GridFF</text><g class="layers" style="opacity: 0.3;"><g class="legendfill"/><g class="legendlines"><path class="js-line" d="M5,0h30" style="fill: none; stroke: rgb(0, 128, 0); stroke-opacity: 1; stroke-width: 2px;"/></g><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 1; stroke-width: 2px; fill: rgb(0, 128, 0); fill-opacity: 1; stroke: rgb(0, 128, 0); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-10.6" width="107.640625" height="21.2" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g></g></g><rect class="scrollbar" rx="20" ry="3" width="0" height="0" x="0" y="0" style="fill: rgb(128, 139, 164); fill-opacity: 1;"/></g><g class="g-gtitle"><text class="gtitle" x="35" y="50" text-anchor="start" dy="0em" style="opacity: 1; font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 24px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Evaluations per second vs. Surface Size</text></g><g class="g-xtitle"><text class="xtitle" x="350" y="646.1" text-anchor="middle" style="opacity: 1; font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 18px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Surface Atoms</text></g><g class="g-ytitle"><text class="ytitle" transform="rotate(-90,34.184375,350)" x="34.184375" y="350" text-anchor="middle" style="opacity: 1; font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 18px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Evaluations per second</text></g></g><g class="menulayer"><g class="updatemenu-container" style="cursor: pointer;"><g class="updatemenu-header-group" pointer-events="all"/><g class="updatemenu-dropdown-button-group" updatemenu-active-index="-1" style="pointer-events: all;"><rect class="scrollbox-bg" opacity="0" x="0" y="0" width="0" height="0" style="pointer-events: all;"/></g></g></g></svg>