#!/usr/bin/env python3
"""
Generate test data for demonstration of linear scaling with surface size
"""

import subprocess
import os
import time as time_module

def main():
    # Create results directories
    os.makedirs("results/all", exist_ok=True)
    os.makedirs("results/best", exist_ok=True)
    
    # Parameters to test - generate full range 1-16
    surface_sizes = [f"NaCl_{i}x{i}" for i in range(1, 17)]
    perframe_values = [20, 500]
    perVF_values = [20, 50]
    nSys_values = [1000, 5000]
    gridnPBC = "(1,1,0)"
    
    results_data = []
    
    print("Generating test data for linear scaling demonstration...")
    print("=" * 60)
    
    total_combinations = len(surface_sizes) * len(perframe_values) * len(perVF_values) * len(nSys_values)
    current = 0

    for surf_name in surface_sizes:
        for perframe in perframe_values:
            for perVF in perVF_values:
                for nSys in nSys_values:
                    current += 1
                    print(f"[{current}/{total_combinations}] Processing {surf_name}, nSys={nSys}, perframe={perframe}, perVF={perVF}")

                    # Run simulation
                    cmd = [
                        "python3", "run_throughput_MD.py",
                        "--nSys", str(nSys),
                        "--surf_name", surf_name,
                        "--perframe", str(perframe),
                        "--perVF", str(perVF),
                        "--gridnPBC", gridnPBC,
                        "--dovdW", "1",
                        "--doSurfAtoms", "1",
                        "--GridFF", "1",
                        "--Fconv", "0.01"
                    ]
                
                result = subprocess.run(cmd, capture_output=True, text=True)
                
                if result.returncode == 0:
                    # Read the generated minima.dat
                    try:
                        with open("minima.dat", "r") as f:
                            lines = f.readlines()
                            for line in lines:
                                if line.startswith("1 "):  # Data line
                                    parts = line.strip().split()
                                    energy = parts[1]
                                    force = parts[2]
                                    time = parts[3]
                                    steps = parts[4]
                                    throughput = parts[5]
                                    
                                    # Create filename in expected format
                                    # Ensure nPBC keeps the full format with parentheses
                                    nPBC_formatted = gridnPBC  # Keep the full "(1,1,0)" format
                                    filename = f"minima__1110_surf:_{surf_name}_nPBC_{nPBC_formatted}_nloc:_MMFF_32_move_32_NBFF_--_surf_--_gridFF_--_gridFFbSpline_--___replica:_{nSys}_perframe:_{perframe}_perVF:_{perVF}"
                                    
                                    # Add to results
                                    result_line = f"{filename} 1 {energy} {force} {time} {steps} {throughput}"
                                    results_data.append(result_line)

                                    print(f"  → Energy: {energy}, Time: {time}s, Throughput: {throughput}")
                                    break
                    except Exception as e:
                        print(f"  → Error reading results: {e}")
                else:
                    print(f"  → Simulation failed: {result.stderr}")

                # Small delay to make it feel more realistic
                time_module.sleep(0.1)
    
    # Write all results to results.dat
    results_file = "results/all/results.dat"
    with open(results_file, "w") as f:
        for line in results_data:
            f.write(line + "\n")
    
    # Also copy to root directory for plot_data.py
    with open("results.dat", "w") as f:
        for line in results_data:
            f.write(line + "\n")
    
    print("=" * 60)
    print(f"Generated {len(results_data)} data points")
    print(f"Results saved to: {results_file}")
    print("Ready for visualization!")
    print("\nNext steps:")
    print("1. Run: python3 throughput_gui.py")
    print("2. Go to 'Visualization' tab")
    print("3. Click 'Generate Interactive Plot'")

if __name__ == "__main__":
    main()
